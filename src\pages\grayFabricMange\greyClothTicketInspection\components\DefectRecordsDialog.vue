<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DefectInfoDialog from './DefectInfoDialog.vue'
import Table from '@/components/Table.vue'

const emits = defineEmits(['onUpdate', 'onDelete'])

// DefectInfoDialog引用
const defectInfoDialogRef = ref()

const state = reactive({
  showModal: false,
  modalName: '疵点记录',
  currentDefectName: '',
  records: [] as Array<{
    id: string
    barcode: string
    name: string
    unitName: string
    position: number
    count: number
    score: number
    timestamp: string
    measurement_unit_id?: number
    voucher_files?: string[]
    apiId?: number // API返回的ID
  }>,
})

// 表格列配置
const columns = [
  {
    title: '条形码',
    field: 'barcode',
    align: 'center',
  },
  {
    title: '疵点名称',
    field: 'name',
    align: 'center',
  },
  {
    title: '单位名称',
    field: 'unitName',
    align: 'center',
  },
  {
    title: '疵点位置',
    field: 'position',
    soltName: 'position',
    align: 'center',
  },
  {
    title: '疵点数量',
    field: 'count',
    align: 'center',
  },
  {
    title: '分数',
    field: 'score',
    align: 'center',
  },
]

// 表格数据
const tableData = computed(() => {
  return state.records.map(record => ({
    ...record,
    unitName: '个', // 默认单位
  }))
})

// 显示弹框
function showDialog(defectName: string, records: any[]) {
  if (!defectName) {
    state.currentDefectName = ''
    state.modalName = `疵点记录`
    state.records = records
    state.showModal = true
    return
  }
  state.currentDefectName = defectName
  state.modalName = `疵点记录 - ${defectName}`
  state.records = records.filter(record => record.name === defectName)
  state.showModal = true
}

// 编辑疵点
function handleEdit(row: any) {
  // 构造编辑数据，匹配DefectInfoDialog的数据格式
  const editData = {
    id: row.id,
    name: row.name,
    defect_name: row.name,
    defect_count: row.count,
    defect_position: row.position,
    score: row.score.toString(),
    measurement_unit_id: row.measurement_unit_id || 0,
    measurement_unit_name: row.unitName || '个',
    voucher_files: row.voucher_files || [],
  }

  // 判断是否为"其他"类型
  const isOther = row.name === '其他'

  // 打开编辑弹框
  defectInfoDialogRef.value?.showAddDialog(editData, isOther, false)
}

// 删除疵点
function handleDelete(row: any) {
  // 直接触发删除事件，让父组件处理删除逻辑（包括API调用和确认对话框）
  emits('onDelete', row)
}

// 处理编辑确认
function handleEditSure(editedData: any) {
  // 更新当前记录
  const index = state.records.findIndex(record => record.id === editedData.id)
  if (index > -1) {
    // 更新记录数据
    state.records[index] = {
      ...state.records[index],
      name: editedData.defect_name || editedData.name,
      count: editedData.defect_count,
      position: editedData.defect_position,
      score: editedData.score,
      measurement_unit_id: editedData.measurement_unit_id,
      unitName: editedData.measurement_unit_name,
      voucher_files: editedData.voucher_files,
    }

    // 触发更新事件，传递给父组件处理
    emits('onUpdate', {
      ...editedData,
      originalId: editedData.id,
      apiId: state.records[index].apiId, // 传递API ID用于更新操作
    })

    ElMessage.success('疵点信息更新成功')
  }
}

// 关闭弹框
function handleClose() {
  state.showModal = false
}

const tableConfig = ref({
  showSlotNums: true,
  showCheckBox: false,
  showOperate: true,
  operateWidth: '120',
  showSort: false,
  height: 'auto',
})
defineExpose({
  showDialog,
  state,
})
</script>

<template>
  <vxe-modal
    v-model="state.showModal"
    :title="state.modalName"
    width="800"
    height="600"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    @hide="handleClose"
  >
    <!-- 表格 -->
    <Table
      :table-list="tableData"
      :column-list="columns"
      :config="tableConfig"
    >
      <!-- 疵点位置插槽 -->
      <template #position="{ row }">
        <span>第{{ row.position }}米</span>
      </template>

      <!-- 操作插槽 -->
      <template #operate="{ row }">
        <el-button
          type="primary"
          size="small"
          link
          @click="handleEdit(row)"
        >
          修改
        </el-button>
        <el-button
          type="danger"
          size="small"
          link
          @click="handleDelete(row)"
        >
          删除
        </el-button>
      </template>
    </Table>
  </vxe-modal>

  <!-- 编辑疵点信息弹框 -->
  <DefectInfoDialog
    ref="defectInfoDialogRef"
    @handle-sure="handleEditSure"
  />
</template>

<style lang="scss" scoped>

</style>
