<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DefectInfoDialog from './DefectInfoDialog.vue'
import Table from '@/components/Table.vue'
import FileCard from '@/components/UploadFile/FileCard/index.vue'
import { formatLengthDiv, formatLengthMul } from '@/common/format'
import { formatUrl } from '@/common/util'

const emits = defineEmits(['onUpdate', 'onDelete'])

// DefectInfoDialog引用
const defectInfoDialogRef = ref()

const state = reactive({
  showModal: false,
  modalName: '疵点记录',
  currentDefectName: '',
  records: [] as Array<{
    id: string
    barcode: string
    name: string
    unitName: string
    position: number
    count: number
    score: number
    timestamp: string
    measurement_unit_id?: number
    voucher_files?: string[]
    apiId?: number // API返回的ID
  }>,
})

// 表格列配置
const columns = [
  {
    title: '条形码',
    field: 'barcode',
    align: 'center' as const,
  },
  {
    title: '疵点名称',
    field: 'name',
    align: 'center' as const,
  },
  {
    title: '单位名称',
    field: 'unitName',
    align: 'center' as const,
  },
  {
    title: '疵点位置',
    field: 'position',
    soltName: 'position',
    align: 'center' as const,
  },
  {
    title: '疵点数量',
    field: 'count',
    align: 'center' as const,
  },
  {
    title: '分数',
    field: 'score',
    align: 'center' as const,
  },
  {
    title: '上传凭证',
    field: 'voucher_files',
    soltName: 'voucher_files',
    align: 'center' as const,
    width: '120',
  },
]

// 表格数据
const tableData = computed(() => {
  return state.records.map(record => ({
    ...record,
    unitName: '个', // 默认单位
  }))
})

// 显示弹框
function showDialog(defectName: string, records: any[]) {
  if (!defectName) {
    state.currentDefectName = ''
    state.modalName = `疵点记录`
    state.records = records
    state.showModal = true
    return
  }
  state.currentDefectName = defectName
  state.modalName = `疵点记录 - ${defectName}`
  state.records = records.filter(record => record.name === defectName)
  state.showModal = true
}

// 编辑疵点
function handleEdit(row: any) {
  // 构造编辑数据，匹配DefectInfoDialog的数据格式
  const editData = {
    id: row.id,
    name: row.name,
    defect_name: row.name,
    defect_count: row.count,
    defect_position: formatLengthDiv(row.position),
    score: row.score.toString(),
    measurement_unit_id: row.measurement_unit_id || 0,
    measurement_unit_name: row.unitName || '个',
    voucher_files: row.voucher_files || [],
  }

  // 判断是否为"其他"类型
  const isOther = row.name === '其他'

  // 打开编辑弹框
  defectInfoDialogRef.value?.showAddDialog(editData, isOther, false)
}

// 删除疵点
function handleDelete(row: any) {
  // 直接触发删除事件，让父组件处理删除逻辑（包括API调用和确认对话框）
  emits('onDelete', row)
}

// 处理编辑确认
function handleEditSure(editedData: any) {
  // 更新当前记录
  const index = state.records.findIndex(record => record.id === editedData.id)
  if (index > -1) {
    // 更新记录数据
    state.records[index] = {
      ...state.records[index],
      name: editedData.defect_name || editedData.name,
      count: editedData.defect_count,
      position: formatLengthMul(editedData.defect_position),
      score: editedData.score,
      measurement_unit_id: editedData.measurement_unit_id,
      unitName: editedData.measurement_unit_name,
      voucher_files: editedData.voucher_files,
    }

    // 触发更新事件，传递给父组件处理
    emits('onUpdate', {
      ...editedData,
      originalId: editedData.id,
      apiId: state.records[index].apiId, // 传递API ID用于更新操作
    })

    ElMessage.success('疵点信息更新成功')
  }
}

// 关闭弹框
function handleClose() {
  state.showModal = false
}

const tableConfig = ref({
  showSlotNums: true,
  showCheckBox: false,
  showOperate: true,
  operateWidth: '120',
  showSort: false,
  height: 'auto',
})
defineExpose({
  showDialog,
  state,
})
</script>

<template>
  <vxe-modal
    v-model="state.showModal"
    :title="state.modalName"
    width="800"
    height="600"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    @hide="handleClose"
  >
    <!-- 表格 -->
    <Table
      :table-list="tableData"
      :column-list="columns"
      :config="tableConfig"
    >
      <!-- 疵点位置插槽 -->
      <template #position="{ row }">
        <span>第{{ formatLengthDiv(row.position) }}米</span>
      </template>

      <!-- 上传凭证插槽 -->
      <template #voucher_files="{ row }">
        <div v-if="row.voucher_files && row.voucher_files.length > 0" class="voucher-files-container">
          <FileCard
            v-for="(file, index) in row.voucher_files.map(item => formatUrl(item)).slice(0, 3)"
            :key="index"
            :file-url="file"
            :all-urls="row.voucher_files.map(item => formatUrl(item))"
            clear-disabled
            drown-disabled
            default-disabled
            class="file-card-item"
          />
          <div
            v-if="row.voucher_files.length > 3"
            class="more-files-indicator"
          >
            <span class="more-files-text">+{{ row.voucher_files.length - 3 }}</span>
          </div>
        </div>
        <span v-else class="no-files">无凭证</span>
      </template>

      <!-- 操作插槽 -->
      <template #operate="{ row }">
        <el-button
          type="primary"
          size="small"
          link
          @click="handleEdit(row)"
        >
          修改
        </el-button>
        <el-button
          type="danger"
          size="small"
          link
          @click="handleDelete(row)"
        >
          删除
        </el-button>
      </template>
    </Table>
  </vxe-modal>

  <!-- 编辑疵点信息弹框 -->
  <DefectInfoDialog
    ref="defectInfoDialogRef"
    @handle-sure="handleEditSure"
  />
</template>

<style lang="scss" scoped>
.voucher-files-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  justify-content: center;
  padding: 4px;
  max-width: 120px;
  overflow: hidden;
}

.file-card-item {
  width: 32px !important;
  height: 32px !important;
  flex-shrink: 0;

  :deep(.fileCard) {
    width: 32px;
    height: 32px;
    margin: 0;
    border-radius: 4px;

    .item-thumbnail {
      width: 32px;
      height: 32px;
      border-radius: 4px;
    }
  }

  :deep(.el-image) {
    border-radius: 4px;
  }

  :deep(.el-link) {
    font-size: 10px;
    padding: 2px;
    text-align: center;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.more-files-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  flex-shrink: 0;
}

.more-files-text {
  font-size: 10px;
  color: #909399;
  font-weight: 500;
}

.no-files {
  color: #999;
  font-size: 12px;
  font-style: italic;
  text-align: center;
  padding: 8px;
}
</style>
