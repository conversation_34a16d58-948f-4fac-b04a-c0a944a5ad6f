# isNaN 修复总结

## 问题描述

在代码审查中发现 `src/use/useGlobalConfig.ts` 文件中使用了 `isNaN` 而不是推荐的 `Number.isNaN`。

## 问题原因

`isNaN` 和 `Number.isNaN` 的区别：

- **`isNaN(value)`**: 会先尝试将参数转换为数字，然后判断是否为 NaN
- **`Number.isNaN(value)`**: 直接判断参数是否严格等于 NaN，不进行类型转换

### 示例差异

```javascript
isNaN('hello')        // true (字符串被转换为 NaN)
Number.isNaN('hello') // false (字符串不是 NaN)

isNaN(NaN)            // true
Number.isNaN(NaN)     // true

isNaN(undefined)      // true (undefined 转换为 NaN)
Number.isNaN(undefined) // false (undefined 不是 NaN)
```

## 修复内容

### 1. ConfigValueConverter.toNumber 方法

**修复前：**
```typescript
static toNumber(value: string): number {
  const num = Number(value)
  return isNaN(num) ? 0 : num
}
```

**修复后：**
```typescript
static toNumber(value: string): number {
  const num = Number(value)
  return Number.isNaN(num) ? 0 : num
}
```

### 2. ConfigValueConverter.toNumberArray 方法

**修复前：**
```typescript
static toNumberArray(value: string, separator: string = ','): number[] {
  return ConfigValueConverter.toArray(value, separator)
    .map(item => Number(item))
    .filter(num => !isNaN(num))
}
```

**修复后：**
```typescript
static toNumberArray(value: string, separator: string = ','): number[] {
  return ConfigValueConverter.toArray(value, separator)
    .map(item => Number(item))
    .filter(num => !Number.isNaN(num))
}
```

### 3. ConfigValueConverter.autoConvert 方法

**修复前：**
```typescript
if (!isNaN(Number(value)))
  return ConfigValueConverter.toNumber(value)
```

**修复后：**
```typescript
if (!Number.isNaN(Number(value)))
  return ConfigValueConverter.toNumber(value)
```

## 修复验证

### 1. 语法检查
- ✅ 无 TypeScript 类型错误
- ✅ 无语法错误
- ✅ 代码格式正确

### 2. 功能验证
- ✅ 数字转换功能正常
- ✅ 数组转换功能正常
- ✅ 自动类型转换功能正常

### 3. 行为一致性
由于我们的使用场景中，传入的都是通过 `Number()` 转换后的值，所以 `isNaN` 和 `Number.isNaN` 在这些特定情况下的行为是一致的。但使用 `Number.isNaN` 更加严格和安全。

## 最佳实践

### 推荐使用 Number.isNaN 的原因

1. **类型安全**: 不会进行隐式类型转换
2. **行为明确**: 只有真正的 NaN 值才返回 true
3. **性能更好**: 避免了类型转换的开销
4. **代码更清晰**: 意图更加明确

### 使用场景对比

```typescript
// 检查一个值是否为 NaN（推荐）
if (Number.isNaN(value)) {
  // 处理 NaN 情况
}

// 检查一个值转换为数字后是否为 NaN（不推荐）
if (isNaN(value)) {
  // 可能会有意外的类型转换
}

// 检查字符串转换为数字是否有效（正确做法）
const num = Number(value)
if (Number.isNaN(num)) {
  // 处理无效数字
}
```

## 影响范围

### 修复的文件
- `src/use/useGlobalConfig.ts` - 全局配置管理工具

### 影响的功能
- 全局配置值的类型转换
- 字符串到数字的转换
- 字符串到数字数组的转换
- 自动类型转换逻辑

### 兼容性
- ✅ 向后兼容
- ✅ 功能行为保持一致
- ✅ 不影响现有代码

## 其他发现

在代码库中还发现了其他文件中使用 `Number.isNaN` 的正确示例：

- `src/util/tableFooterCount.ts` - 已正确使用 `Number.isNaN`
- `src/common/format.ts` - 已正确使用 `Number.isNaN`
- 多个页面组件 - 已正确使用 `Number.isNaN`

这说明项目中大部分代码已经遵循了最佳实践，只有 `useGlobalConfig.ts` 中的几处需要修复。

## 结论

已成功修复 `useGlobalConfig.ts` 文件中的所有 `isNaN` 使用，替换为更安全和规范的 `Number.isNaN`。修复后的代码：

- ✅ 遵循 ESLint 最佳实践
- ✅ 提高了类型安全性
- ✅ 保持了功能的一致性
- ✅ 符合现代 JavaScript 标准

这次修复提高了代码质量，使全局配置管理工具更加健壮和可靠。
