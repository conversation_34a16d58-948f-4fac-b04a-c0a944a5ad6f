import { computed, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { BatchUpdateGlobalConfigList, GetGlobalConfigDropdownList } from '@/api/globalConfig'
import { GlobalEnum } from '@/common/enum'

// 全局配置缓存
const globalConfigCache = reactive<Map<number, Api.GetGlobalConfigDropdownList.Response>>(new Map())
const cacheTimestamp = ref<number>(0)
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

// 配置类型枚举
export enum ConfigType {
  TEXT = 1, // 文本
  RADIO = 2, // 单选
  CHECKBOX = 3, // 多选
  SWITCH = 4, // 开关
}

// 配置值类型
export type ConfigValue = string | number | boolean | string[] | number[]

// 配置项接口
export interface GlobalConfigItem {
  id: number
  key: string
  description: string
  options: string
  type: ConfigType
  value: ConfigValue
}

// 类型转换工具
export class ConfigValueConverter {
  /**
   * 将字符串转换为布尔值
   */
  static toBoolean(value: string): boolean {
    return value === 'true' || value === '1'
  }

  /**
   * 将字符串转换为数字
   */
  static toNumber(value: string): number {
    const num = Number(value)
    return Number.isNaN(num) ? 0 : num
  }

  /**
   * 将字符串转换为数组
   */
  static toArray(value: string, separator: string = ','): string[] {
    return value ? value.split(separator).map(item => item.trim()) : []
  }

  /**
   * 将字符串转换为数字数组
   */
  static toNumberArray(value: string, separator: string = ','): number[] {
    return ConfigValueConverter.toArray(value, separator)
      .map(item => Number(item))
      .filter(num => !Number.isNaN(num))
  }

  /**
   * 自动类型转换
   */
  static autoConvert(value: string, type: ConfigType): ConfigValue {
    switch (type) {
      case ConfigType.TEXT:
        return value
      case ConfigType.RADIO:
        // 单选可能是布尔值或数字
        if (value === 'true' || value === 'false')
          return ConfigValueConverter.toBoolean(value)

        if (!Number.isNaN(Number(value)))
          return ConfigValueConverter.toNumber(value)

        return value
      case ConfigType.CHECKBOX:
        return ConfigValueConverter.toArray(value)
      case ConfigType.SWITCH:
        return ConfigValueConverter.toBoolean(value)
      default:
        return value
    }
  }
}

/**
 * 全局配置管理 Composable
 */
export function useGlobalConfig() {
  const { fetchData: getGlobalConfig, data: globalConfigData, success: globalConfigSuccess, msg: globalConfigMsg, loading } = GetGlobalConfigDropdownList()
  const { fetchData: saveConfig, success: saveSuccess, msg: saveMsg, loading: saveLoading } = BatchUpdateGlobalConfigList()

  /**
   * 检查缓存是否有效
   */
  const isCacheValid = computed(() => {
    return Date.now() - cacheTimestamp.value < CACHE_DURATION
  })

  /**
   * 获取单个配置值
   */
  async function getConfigValue<T = ConfigValue>(
    configId: number | GlobalEnum,
    defaultValue?: T,
    autoConvert: boolean = true,
  ): Promise<T> {
    const configs = await getConfigs([configId])
    const config = configs.find(item => item.id === configId)

    if (!config)
      return defaultValue as T

    if (autoConvert)
      return ConfigValueConverter.autoConvert(config.options, config.type as ConfigType) as T

    return config.options as T
  }

  /**
   * 获取多个配置值
   */
  async function getConfigValues(
    configIds: (number | GlobalEnum)[],
    autoConvert: boolean = true,
  ): Promise<Record<number, ConfigValue>> {
    const configs = await getConfigs(configIds)
    const result: Record<number, ConfigValue> = {}

    configs.forEach((config) => {
      if (autoConvert)
        result[config.id] = ConfigValueConverter.autoConvert(config.options, config.type as ConfigType)
      else
        result[config.id] = config.options
    })

    return result
  }

  /**
   * 获取配置详情列表
   */
  async function getConfigs(configIds: (number | GlobalEnum)[]): Promise<Api.GetGlobalConfigDropdownList.Response[]> {
    const ids = configIds.map(id => Number(id))

    // 检查缓存
    if (isCacheValid.value) {
      const cachedConfigs = ids.map(id => globalConfigCache.get(id)).filter(Boolean) as Api.GetGlobalConfigDropdownList.Response[]
      if (cachedConfigs.length === ids.length)
        return cachedConfigs
    }

    // 从API获取
    try {
      await getGlobalConfig({
        ids: ids.join(','),
      })

      if (!globalConfigSuccess.value) {
        ElMessage.error(globalConfigMsg.value || '获取全局配置失败')
        return []
      }

      const configs = globalConfigData.value?.list || []

      // 更新缓存
      configs.forEach((config) => {
        if (config.id)
          globalConfigCache.set(config.id, config)
      })
      cacheTimestamp.value = Date.now()

      return configs
    }
    catch (error) {
      console.error('获取全局配置失败:', error)
      ElMessage.error('获取全局配置失败')
      return []
    }
  }

  /**
   * 更新配置值
   */
  async function updateConfigValue(
    configId: number | GlobalEnum,
    value: ConfigValue,
  ): Promise<boolean> {
    try {
      const stringValue = Array.isArray(value) ? value.join(',') : String(value)

      await saveConfig({
        update_global_config_list: [{
          id: Number(configId),
          options: stringValue,
        }],
      })

      if (saveSuccess.value) {
        // 清除缓存中的对应项
        globalConfigCache.delete(Number(configId))
        ElMessage.success('配置更新成功')
        return true
      }
      else {
        ElMessage.error(saveMsg.value || '配置更新失败')
        return false
      }
    }
    catch (error) {
      console.error('更新配置失败:', error)
      ElMessage.error('更新配置失败')
      return false
    }
  }

  /**
   * 批量更新配置值
   */
  async function updateConfigValues(
    configs: Record<number | GlobalEnum, ConfigValue>,
  ): Promise<boolean> {
    try {
      const updateList = Object.entries(configs).map(([id, value]) => ({
        id: Number(id),
        options: Array.isArray(value) ? value.join(',') : String(value),
      }))

      await saveConfig({
        update_global_config_list: updateList,
      })

      if (saveSuccess.value) {
        // 清除缓存中的对应项
        Object.keys(configs).forEach((id) => {
          globalConfigCache.delete(Number(id))
        })
        ElMessage.success('配置批量更新成功')
        return true
      }
      else {
        ElMessage.error(saveMsg.value || '配置批量更新失败')
        return false
      }
    }
    catch (error) {
      console.error('批量更新配置失败:', error)
      ElMessage.error('批量更新配置失败')
      return false
    }
  }

  /**
   * 清除缓存
   */
  function clearCache(): void {
    globalConfigCache.clear()
    cacheTimestamp.value = 0
  }

  /**
   * 预加载常用配置
   */
  async function preloadCommonConfigs(): Promise<void> {
    const commonConfigIds = [
      GlobalEnum.AutoSave,
      GlobalEnum.AutoSaveSeconds,
      GlobalEnum.NoWeaverSelection,
      GlobalEnum.NoInspectorSelection,
      GlobalEnum.WeightReflection,
      GlobalEnum.StableValue,
    ]

    await getConfigs(commonConfigIds)
  }

  return {
    // 状态
    loading,
    saveLoading,

    // 方法
    getConfigValue,
    getConfigValues,
    getConfigs,
    updateConfigValue,
    updateConfigValues,
    clearCache,
    preloadCommonConfigs,

    // 工具类
    ConfigValueConverter,
    ConfigType,
  }
}

/**
 * 电子秤设置专用 Composable
 */
export function useElectronicScaleConfig() {
  const { getConfigValues, updateConfigValues } = useGlobalConfig()

  // 电子秤配置ID映射
  const scaleConfigIds = {
    autoSaveSeconds: GlobalEnum.AutoSaveSeconds,
    noWeaverSelection: GlobalEnum.NoWeaverSelection,
    noInspectorSelection: GlobalEnum.NoInspectorSelection,
    weightReflection: GlobalEnum.WeightReflection,
    dataHead: GlobalEnum.DataHead,
    dataEnd: GlobalEnum.DataEnd,
    scanCodeReading: GlobalEnum.ScanCodeReading,
    stableValue: GlobalEnum.StableValue,
    autoSave: GlobalEnum.AutoSave,
  }

  /**
   * 获取电子秤设置
   */
  async function getElectronicScaleSettings() {
    const configIds = Object.values(scaleConfigIds)
    const configs = await getConfigValues(configIds)

    return {
      autoSaveSeconds: configs[scaleConfigIds.autoSaveSeconds] as number || 3,
      noWeaverSelection: configs[scaleConfigIds.noWeaverSelection] as boolean || false,
      noInspectorSelection: configs[scaleConfigIds.noInspectorSelection] as boolean || false,
      weightReflection: configs[scaleConfigIds.weightReflection] as boolean || false,
      dataHead: configs[scaleConfigIds.dataHead] as string || '',
      dataEnd: configs[scaleConfigIds.dataEnd] as string || '',
      scanCodeReading: configs[scaleConfigIds.scanCodeReading] as boolean || false,
      stableValue: configs[scaleConfigIds.stableValue] as number || 0,
      autoSave: configs[scaleConfigIds.autoSave] as boolean || false,
    }
  }

  /**
   * 保存电子秤设置
   */
  async function saveElectronicScaleSettings(settings: any) {
    const updateConfigs: Record<number, ConfigValue> = {}

    Object.entries(scaleConfigIds).forEach(([key, configId]) => {
      if (settings[key] !== undefined)
        updateConfigs[configId] = settings[key]
    })

    return await updateConfigValues(updateConfigs)
  }

  return {
    getElectronicScaleSettings,
    saveElectronicScaleSettings,
    scaleConfigIds,
  }
}
