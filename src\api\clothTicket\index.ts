import type { ResponseList } from '@/api/commonTs/index'
import { useRequest } from '@/use/useRequest'

// 获取生产排产单细码详情
export function GetFineCodeDetail() {
  return useRequest<Api.GetFineCodeDetail.Request, Api.GetFineCodeDetail.Response>({
    url: '/admin/v1/produce/productionScheduleOrder/getFineCodeDetail',
    method: 'get',
  })
}
// 生产排产单细码称重
export function WeighingFineCode() {
  return useRequest<Api.WeighingFineCode.Request, Api.WeighingFineCode.Response>({
    url: '/admin/v1/produce/productionScheduleOrder/weighingFineCode',
    method: 'put',
  })
}
// 生产排产单细码验布称重
export function InspectingFineCode() {
  return useRequest<Api.InspectingFineCode.Request, Api.InspectingFineCode.Response>({
    url: '/admin/v1/produce/productionScheduleOrder/inspectingFineCode',
    method: 'put',
  })
}
// 获取坯布质检信息
export function GetGfmQualityCheck() {
  return useRequest<Api.GetGfmQualityCheck.Request, Api.GetGfmQualityCheck.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmQualityCheck',
    method: 'get',
  })
}
// 获取坯布质检疵点
export function GetGfmQualityCheckDefect() {
  return useRequest<Api.GetGfmQualityCheckDefect.Request, Api.GetGfmQualityCheckDefect.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmQualityCheckDefect',
    method: 'get',
  })
}
// 更新坯布质检疵点
export function UpdateGfmQualityCheckDefect() {
  return useRequest<Api.UpdateGfmQualityCheckDefect.Request, Api.UpdateGfmQualityCheckDefect.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmQualityCheckDefect',
    method: 'put',
  })
}
// 删除坯布质检疵点
export function DeleteGfmQualityCheckDefect() {
  return useRequest<Api.DeleteGfmQualityCheckDefect.Request, Api.DeleteGfmQualityCheckDefect.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmQualityCheckDefect',
    method: 'delete',
  })
}
// 添加坯布质检疵点
export function AddGfmQualityCheckDefect() {
  return useRequest<Api.AddGfmQualityCheckDefect.Request, Api.AddGfmQualityCheckDefect.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmQualityCheckDefect',
    method: 'post',
  })
}
// 获取坯布质检疵点列表
export function GetGfmQualityCheckDefectList() {
  return useRequest<Api.GetGfmQualityCheckDefectList.Request, Api.GetGfmQualityCheckDefectList.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmQualityCheckDefect/list',
    method: 'get',
  })
}
