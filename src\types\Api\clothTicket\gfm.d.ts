declare namespace Api.InspectingFineCode {
/**
 * produce.InspectingProductionScheduleOrderFineCodeParam
 */
  export interface Request {
    /**
     * 实际重量
     */
    actual_weight: number
    /**
     * 电子秤重量
     */
    electronic_scale_weight: number
    /**
     * 细码ID
     */
    id: number
    /**
     * 验布人id
     */
    inspector_id?: number
    /**
     * 验布人名称
     */
    inspector_name?: string
    /**
     * 织工ID
     */
    weaver_id: number
    /**
     * 织工名称
     */
    weaver_name: string
    [property: string]: any
  }
  /**
   * produce.WeighingProductionScheduleOrderFineCodeData
   */
  export interface Response {
    /**
     * 条码
     */
    fabric_piece_code?: string
    /**
     * 坯布id
     */
    grey_fabric_id?: number
    /**
     * 更新的生产排产单细码表ID
     */
    id?: number
    /**
     * 卷号
     */
    volume_number?: number
    [property: string]: any
  }
}
declare namespace Api.GetGfmQualityCheck{
  export interface Request {
    /**
     * 条形码
     */
    bar_code?: string
    /**
     * id
     */
    id?: number
    /**
     * 检查员id
     */
    quality_checker_id?: number
    [property: string]: any
  }
  /**
   * grey_fabric_manage.GetGfmQualityCheckData
   */
  export interface Response {
    /**
     * 实际重量
     */
    actually_weight?: number
    /**
     * 条形码
     */
    bar_code?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 疵点信息
     */
    defect_merge_str?: string
    /**
     * 用户所属部门
     */
    department_id?: number
    /**
     * 疵点信息
     */
    detail_list?: GreyFabricManageGetGfmQualityCheckDefectData[]
    /**
     * 坯布编号
     */
    grey_fabric_code?: string
    /**
     * 坯布id
     */
    grey_fabric_id?: number
    /**
     * 坯布名称
     */
    grey_fabric_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 质检时间
     */
    quality_check_date?: string
    /**
     * 质检员id（关联user.id）
     */
    quality_checker_id?: number
    /**
     * 质检员名称
     */
    quality_checker_name?: string
    /**
     * 质检备注
     */
    remark?: string
    /**
     * 条数
     */
    roll?: number
    /**
     * 总评分
     */
    total_score?: number
    /**
     * 单位id
     */
    unit_id?: number
    /**
     * 单位名称
     */
    unit_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 卷号
     */
    volume_number?: number
    /**
     * 数量
     */
    weight?: number
    [property: string]: any
  }

  /**
   * grey_fabric_manage.GetGfmQualityCheckDefectData
   */
  export interface GreyFabricManageGetGfmQualityCheckDefectData {
    /**
     * 条形码
     */
    bar_code?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 疵点个数
     */
    defect_count?: number
    /**
     * 疵点id
     */
    defect_id?: number
    /**
     * 疵点名
     */
    defect_name?: string
    /**
     * 疵点位置
     */
    defect_position?: number
    /**
     * 验布员（关联user.id）
     */
    fabric_inspector_id?: number
    /**
     * 验布员名称
     */
    fabric_inspector_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 计量单位id
     */
    measurement_unit_id?: number
    /**
     * 计量单位名称
     */
    measurement_unit_name?: string
    /**
     * 质检id
     */
    pid?: number
    /**
     * 评分
     */
    score?: number
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }
}
declare namespace Api.GetGfmQualityCheckDefect {
  export interface Request {
    /**
     * id
     */
    id: number
    [property: string]: any
  }
  /**
   * grey_fabric_manage.GetGfmQualityCheckDefectData
   */
  export interface Response {
    /**
     * 条形码
     */
    bar_code?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 疵点个数
     */
    defect_count?: number
    /**
     * 疵点id
     */
    defect_id?: number
    /**
     * 疵点名
     */
    defect_name?: string
    /**
     * 疵点位置
     */
    defect_position?: number
    /**
     * 验布员（关联user.id）
     */
    fabric_inspector_id?: number
    /**
     * 验布员名称
     */
    fabric_inspector_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 计量单位id
     */
    measurement_unit_id?: number
    /**
     * 计量单位名称
     */
    measurement_unit_name?: string
    /**
     * 质检id
     */
    pid?: number
    /**
     * 评分
     */
    score?: number
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }
}
declare namespace Api.UpdateGfmQualityCheckDefect {
  /**
   * grey_fabric_manage.UpdateGfmQualityCheckDefectParam
   */
  export interface Request {
    /**
     * 条形码
     */
    bar_code?: string
    /**
     * 疵点个数
     */
    defect_count?: number
    /**
     * 疵点id
     */
    defect_id?: number
    /**
     * 疵点名称(其他疵点录入使用)
     */
    defect_name?: string
    /**
     * 疵点位置
     */
    defect_position?: number
    /**
     * 验布员（关联user.id）
     */
    fabric_inspector_id?: number
    id?: number
    /**
     * 计量单位id(其他疵点录入使用)
     */
    measurement_unit_id?: number
    /**
     * 分数
     */
    score?: number
    [property: string]: any
  }
  /**
   * grey_fabric_manage.UpdateGfmQualityCheckDefectData
   */
  export interface Response {
    id?: number
    [property: string]: any
  }
}
declare namespace Api.DeleteGfmQualityCheckDefect{
  /**
   * grey_fabric_manage.DeleteGfmQualityCheckDefectParam
   */
  export interface Request {
    delete_remark?: string
    id?: number
    [property: string]: any
  }
  /**
   * grey_fabric_manage.DeleteGfmQualityCheckDefectData
   */
  export interface Response {
    id?: number
    [property: string]: any
  }
}
declare namespace Api.AddGfmQualityCheckDefect {
  /**
   * grey_fabric_manage.AddGfmQualityCheckDefectParam
   */
  export interface Request {
    /**
     * 条形码
     */
    bar_code?: string
    /**
     * 疵点个数
     */
    defect_count?: number
    /**
     * 疵点id
     */
    defect_id?: number
    /**
     * 疵点名称(其他疵点录入使用)
     */
    defect_name?: string
    /**
     * 疵点位置
     */
    defect_position?: number
    /**
     * 验布员（关联user.id）
     */
    fabric_inspector_id?: number
    /**
     * 质检疵点信息id(更新使用)
     */
    id?: number
    /**
     * 计量单位id(其他疵点录入使用)
     */
    measurement_unit_id?: number
    /**
     * 质检id
     */
    pid?: number
    /**
     * 分数
     */
    score?: number
    [property: string]: any
  }
  /**
   * grey_fabric_manage.AddGfmQualityCheckDefectData
   */
  export interface Response {
    id?: number
    pid?: number
    [property: string]: any
  }
}
declare namespace Api.GetGfmQualityCheckDefectList {
  /**
   * grey_fabric_manage.GetGfmQualityCheckDefectData
   */
  export interface Response {
    /**
     * 条形码
     */
    bar_code?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 疵点个数
     */
    defect_count?: number
    /**
     * 疵点id
     */
    defect_id?: number
    /**
     * 疵点名
     */
    defect_name?: string
    /**
     * 疵点位置
     */
    defect_position?: number
    /**
     * 验布员（关联user.id）
     */
    fabric_inspector_id?: number
    /**
     * 验布员名称
     */
    fabric_inspector_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 计量单位id
     */
    measurement_unit_id?: number
    /**
     * 计量单位名称
     */
    measurement_unit_name?: string
    /**
     * 质检id
     */
    pid?: number
    /**
     * 评分
     */
    score?: number
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }
  export interface Request {
    /**
     * download
     */
    download?: number
    /**
     * limit
     */
    limit?: number
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * size
     */
    size?: number
    [property: string]: any
  }
}
