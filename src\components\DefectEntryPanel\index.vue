<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import UploadFile from '@/components/UploadFile/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import DigitalKeyboard from '@/components/DigitalKeyboard/index.vue'
import ResizableSplitter from '@/components/ResizableSplitter/index.vue'
import { DictionaryType } from '@/common/enum'
import { formatLengthMul } from '@/common/format'
import { DigitalKeyboardPresets } from '@/components/DigitalKeyboard/types'

// 定义组件的 Props 类型
interface DefectEntryProps {
  /** 是否显示为弹框模式 */
  modalMode?: boolean
  /** 弹框标题 */
  modalTitle?: string
  /** 是否显示弹框 */
  visible?: boolean
  /** 疵点数据 */
  modelValue?: DefectFormData
  /** 是否为"其他"疵点类型 */
  isOther?: boolean
  /** 是否为编辑模式 */
  isEdit?: boolean
  /** 是否显示上传功能 */
  showUpload?: boolean
  /** 是否显示疵点位置 */
  showPosition?: boolean
  /** 是否显示疵点类别 */
  showCategory?: boolean
  /** 是否显示疵点编号 */
  showDefectCode?: boolean
  /** 是否显示数字键盘 */
  showDigitalKeyboard?: boolean
  /** 是否显示分割器 */
  showSplitter?: boolean
  /** 初始分割比例 */
  initialSplit?: number
  /** 额外的表单字段显示配置 */
  extraFields?: string[]
  /** 表单验证规则 */
  customRules?: Record<string, any>
  /** 分数选项 */
  scoreOptions?: number[]
  /** 上传文件类型 */
  uploadAccept?: string
  /** 上传场景 */
  uploadScene?: string
}

// 疵点表单数据类型
interface DefectFormData {
  id?: number
  name?: string
  defect_name?: string
  defect_code?: string
  defect_id?: number
  defect_count?: number | string
  defect_position?: number | string
  measurement_unit_id?: number
  measurement_unit_name?: string
  score?: number | string
  kind_id?: number
  voucher_files?: string[]
  uploadFiles?: string[]
  [key: string]: any
}

// 定义 Props
const props = withDefaults(defineProps<DefectEntryProps>(), {
  modalMode: false,
  modalTitle: '疵点信息',
  visible: false,
  isOther: false,
  isEdit: false,
  showUpload: true,
  showPosition: true,
  showCategory: false,
  showDefectCode: false,
  showDigitalKeyboard: true,
  showSplitter: true,
  initialSplit: 65,
  extraFields: () => [],
  scoreOptions: () => [1, 2, 3, 4],
  uploadAccept: 'image/*,.pdf,.doc,.docx',
  uploadScene: 'defect',
})

// 定义 Emits
const emits = defineEmits<{
  'update:visible': [value: boolean]
  'update:modelValue': [value: DefectFormData]
  'submit': [data: DefectFormData]
  'cancel': []
  'hide': []
}>()

// 表单引用
const formRef = ref()

// 内部状态
const state = reactive({
  showModal: false,
  form: {
    name: '',
    defect_name: '',
    defect_code: '',
    defect_id: 0,
    defect_count: 1,
    defect_position: 0,
    measurement_unit_id: 0,
    measurement_unit_name: '',
    score: 1,
    kind_id: 0,
    voucher_files: [] as string[],
    uploadFiles: [] as string[],
  } as DefectFormData,
})

// 数字键盘相关状态
const editStep = ref(1)
const splitRatio = ref(props.initialSplit)

// 默认验证规则
const defaultRules = {
  defect_count: [{ required: true, message: '请输入疵点个数', trigger: 'blur' }],
  defect_name: [{ required: true, message: '请输入疵点名称', trigger: 'blur' }],
  defect_position: [{ required: true, message: '请输入疵点位置', trigger: 'blur' }],
}

// 合并验证规则
const rules = computed(() => ({
  ...defaultRules,
  ...props.customRules,
}))

// 数字键盘配置
const keyboardConfig = computed(() => ({
  ...DigitalKeyboardPresets.qualityCheck,
  editStep: editStep.value,
  selectedValue: editStep.value === 3 ? state.form.score : undefined,
  disabledNumbers: {
    3: [5, 6, 7, 8, 9, 0], // 第3步只允许1-4
  },
}))

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  state.showModal = newVal
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  if (newVal)
    Object.assign(state.form, newVal)
}, { immediate: true, deep: true })

// 监听内部表单变化，同步到外部
watch(() => state.form, (newVal) => {
  emits('update:modelValue', { ...newVal })
}, { deep: true })

// 显示弹框
function showDialog(data?: DefectFormData, isOther = false, isAdd = true) {
  state.showModal = true
  emits('update:visible', true)

  if (isAdd && data) {
    state.form = {
      ...data,
      id: undefined,
      defect_count: 1,
      defect_position: 0,
      score: 1,
      defect_id: data.id || 0,
      defect_name: data.name || '',
      voucher_files: [],
      uploadFiles: [],
    }
  }
  else if (data) {
    state.form = {
      ...data,
      voucher_files: data.voucher_files || [],
      uploadFiles: data.uploadFiles || [],
    }
  }

  // 重置编辑步骤
  editStep.value = 1
}

// 设置疵点位置
function setDefectPosition(position: number) {
  state.form.defect_position = position
}

// 设置表单数据
function setFormData(data: DefectFormData) {
  Object.assign(state.form, data)
}

// 处理提交
function handleSubmit() {
  if (!formRef.value)
    return

  formRef.value.validate((valid: boolean) => {
    if (!valid)
      return

    const submitData = {
      ...state.form,
      defect_id: state.form.defect_id || 0,
      defect_count: Number(state.form.defect_count),
      defect_position: props.showPosition ? formatLengthMul(state.form.defect_position) : 0,
      score: Number(state.form.score),
      voucher_files: state.form.voucher_files || [],
      uploadFiles: state.form.uploadFiles || [],
    }

    emits('submit', submitData)

    if (props.modalMode)
      handleHide()
  })
}

// 处理取消
function handleCancel() {
  emits('cancel')
  if (props.modalMode)
    handleHide()
}

// 处理隐藏
function handleHide() {
  state.showModal = false
  emits('update:visible', false)
  emits('hide')
}

// 处理单位选择变化
function handleUnitSelectChange(val: any) {
  state.form.measurement_unit_name = val.name
}

// 处理上传文件变化
function handleUploadFilesChange(fileList: string[]) {
  state.form.voucher_files = fileList
  state.form.uploadFiles = fileList
}

// 数字键盘事件处理
function handleNumberInput(num: number) {
  switch (editStep.value) {
    case 1: // 疵点位置
      if (state.form.defect_position === 0 || state.form.defect_position === '0')
        state.form.defect_position = String(num)
      else
        state.form.defect_position += String(num)

      break
    case 2: // 疵点个数
      if (state.form.defect_count === 0 || state.form.defect_count === '0')
        state.form.defect_count = String(num)
      else
        state.form.defect_count += String(num)

      break
    case 3: // 疵点打分 - 只允许1234
      if ([1, 2, 3, 4].includes(num))
        state.form.score = num

      break
  }
}

function handleDotInput() {
  // 只有在疵点位置步骤才允许输入小数点
  if (editStep.value === 1 && !String(state.form.defect_position).includes('.'))
    state.form.defect_position += '.'
}

function handleClear() {
  switch (editStep.value) {
    case 1:
      state.form.defect_position = 0
      break
    case 2:
      state.form.defect_count = 1
      break
    case 3:
      state.form.score = 1
      break
  }
}

function handleBackspace() {
  switch (editStep.value) {
    case 1:
      const posStr = String(state.form.defect_position)
      if (posStr.length > 0)
        state.form.defect_position = posStr.slice(0, -1) || 0

      break
    case 2:
      const countStr = String(state.form.defect_count)
      if (countStr.length > 0)
        state.form.defect_count = countStr.slice(0, -1) || 1

      break
  }
}

function handleStepChange(step: number) {
  editStep.value = step
}

// 处理分割器变化
function handleSplitChange(split: number) {
  splitRatio.value = split
}

// 暴露方法给父组件
defineExpose({
  showDialog,
  setDefectPosition,
  setFormData,
  handleSubmit,
  state,
  formRef,
})
</script>

<template>
  <!-- 弹框模式 -->
  <vxe-modal
    v-if="modalMode"
    v-model="state.showModal"
    :title="modalTitle"
    width="900"
    height="600"
    show-footer
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    @hide="handleHide"
  >
    <!-- 弹框内容使用分割布局 -->
    <div class="defect-entry-modal-content">
      <ResizableSplitter
        v-if="showSplitter && showDigitalKeyboard"
        direction="horizontal"
        :initial-split="splitRatio"
        :min-size="30"
        :max-size="80"
        @split-change="handleSplitChange"
      >
        <template #left>
          <!-- 左侧表单区域 -->
          <div class="form-panel">
            <el-form
              ref="formRef"
              :model="state.form"
              :rules="rules"
              label-width="100px"
              label-position="right"
              size="default"
            >
              <!-- 表单内容 -->
              <slot name="form-content">
                <!-- 疵点编号 -->
                <el-form-item v-if="showDefectCode && isOther" label="疵点编号" prop="defect_code">
                  <el-input
                    v-model="state.form.defect_code"
                    placeholder="疵点编号(非必填)"
                    clearable
                  />
                </el-form-item>

                <!-- 疵点名称 -->
                <el-form-item label="疵点名称" prop="defect_name">
                  <span v-if="!isOther" class="text-gray-800">{{ state.form.defect_name || state.form.name }}</span>
                  <el-input
                    v-else
                    v-model="state.form.defect_name"
                    placeholder="疵点名称"
                    clearable
                  />
                </el-form-item>

                <!-- 单位名称 -->
                <el-form-item label="单位名称" prop="measurement_unit_id">
                  <SelectComponents
                    v-if="isOther"
                    v-model="state.form.measurement_unit_id"
                    style="width: 200px"
                    api="getInfoBaseMeasurementUnitList"
                    label-field="name"
                    value-field="id"
                    clearable
                    @change-value="handleUnitSelectChange"
                  />
                  <span v-else>
                    {{ state.form.measurement_unit_name }}
                  </span>
                </el-form-item>

                <!-- 疵点类别 -->
                <el-form-item v-if="showCategory && isOther" label="疵点类别" prop="kind_id">
                  <SelectComponents
                    v-model="state.form.kind_id"
                    style="width: 200px"
                    api="GetDictionaryDetailEnumListApi"
                    placeholder="请选择疵点种类(非必填)"
                    label-field="name"
                    value-field="id"
                    :query="{ dictionary_id: DictionaryType.defectKind }"
                    clearable
                  />
                </el-form-item>

                <!-- 上传文件 -->
                <el-form-item v-if="showUpload" label="上传凭证" prop="voucher_files">
                  <UploadFile
                    :file-list="state.form.voucher_files"
                    :dragable="true"
                    :multiple="true"
                    :accept="uploadAccept"
                    :secene="uploadScene"
                    :auto-upload="true"
                    @change="handleUploadFilesChange"
                  />
                </el-form-item>
              </slot>
            </el-form>
          </div>
        </template>

        <template #right>
          <!-- 右侧数字键盘区域 -->
          <div class="keyboard-panel">
            <DigitalKeyboard
              :edit-step="editStep"
              :step-titles="keyboardConfig.stepTitles"
              :step-descriptions="keyboardConfig.stepDescriptions"
              :show-dot="keyboardConfig.showDot"
              :show-clear="true"
              :show-backspace="true"
              :show-step-navigation="true"
              :total-steps="keyboardConfig.totalSteps"
              :disabled-numbers="keyboardConfig.disabledNumbers"
              :selected-value="keyboardConfig.selectedValue"
              @number-input="handleNumberInput"
              @dot-input="handleDotInput"
              @clear="handleClear"
              @backspace="handleBackspace"
              @step-change="handleStepChange"
            />
          </div>
        </template>
      </ResizableSplitter>

      <!-- 无分割器时的简单布局 -->
      <div v-else class="simple-layout">
        <el-form
          ref="formRef"
          :model="state.form"
          :rules="rules"
          label-width="100px"
          label-position="right"
          size="default"
        >
          <!-- 简化的表单内容 -->
          <slot name="simple-form" />
        </el-form>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </vxe-modal>

  <!-- 面板模式 -->
  <div v-else class="defect-entry-panel">
    <slot name="header" />

    <!-- 面板内容 -->
    <div class="panel-content">
      <el-form
        ref="formRef"
        :model="state.form"
        :rules="rules"
        label-width="100px"
        label-position="left"
        size="large"
      >
        <!-- 面板表单内容 -->
        <slot name="panel-form" />
      </el-form>
    </div>

    <slot name="footer" />

    <!-- 操作按钮 -->
    <div v-if="!$slots.footer" class="mt-4 flex gap-2">
      <el-button @click="handleCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSubmit">
        确定
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.defect-entry-panel {
  width: 100%;
}

.defect-entry-modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.form-panel {
  padding: 16px;
  overflow-y: auto;
  height: 100%;
}

.keyboard-panel {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.simple-layout {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
  .defect-entry-modal-content {
    flex-direction: column;
  }

  .form-panel,
  .keyboard-panel {
    padding: 8px;
  }
}
</style>
