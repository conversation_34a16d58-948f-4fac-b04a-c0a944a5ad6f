/**
 * 全局配置管理工具使用示例
 *
 * 这个文件展示了如何使用 useGlobalConfig 来简化全局配置的获取和管理
 */

import { onMounted, reactive, ref } from 'vue'
import { ConfigValueConverter, useElectronicScaleConfig, useGlobalConfig } from './useGlobalConfig'
import { GlobalEnum } from '@/common/enum'

// ========== 示例1: 基础用法 ==========

export function basicUsageExample() {
  const { getConfigValue, updateConfigValue } = useGlobalConfig()

  // 获取单个配置值（自动类型转换）
  async function getAutoSaveStatus() {
    const autoSave = await getConfigValue<boolean>(GlobalEnum.AutoSave, false)
    console.log('自动保存状态:', autoSave) // boolean 类型
    return autoSave
  }

  // 获取单个配置值（不自动转换）
  async function getAutoSaveStatusRaw() {
    const autoSave = await getConfigValue<string>(GlobalEnum.AutoSave, 'false', false)
    console.log('自动保存状态(原始):', autoSave) // string 类型
    return autoSave
  }

  // 更新配置值
  async function toggleAutoSave() {
    const currentValue = await getConfigValue<boolean>(GlobalEnum.AutoSave, false)
    const newValue = !currentValue
    const success = await updateConfigValue(GlobalEnum.AutoSave, newValue)
    if (success)
      console.log('自动保存状态已更新为:', newValue)

    return success
  }

  return {
    getAutoSaveStatus,
    getAutoSaveStatusRaw,
    toggleAutoSave,
  }
}

// ========== 示例2: 批量获取配置 ==========

export function batchUsageExample() {
  const { getConfigValues, updateConfigValues } = useGlobalConfig()

  // 批量获取多个配置
  async function getAllSettings() {
    const configIds = [
      GlobalEnum.AutoSave,
      GlobalEnum.AutoSaveSeconds,
      GlobalEnum.NoWeaverSelection,
      GlobalEnum.NoInspectorSelection,
    ]

    const configs = await getConfigValues(configIds)

    return {
      autoSave: configs[GlobalEnum.AutoSave] as boolean,
      autoSaveSeconds: configs[GlobalEnum.AutoSaveSeconds] as number,
      noWeaverSelection: configs[GlobalEnum.NoWeaverSelection] as boolean,
      noInspectorSelection: configs[GlobalEnum.NoInspectorSelection] as boolean,
    }
  }

  // 批量更新配置
  async function saveAllSettings(settings: any) {
    const updateConfigs = {
      [GlobalEnum.AutoSave]: settings.autoSave,
      [GlobalEnum.AutoSaveSeconds]: settings.autoSaveSeconds,
      [GlobalEnum.NoWeaverSelection]: settings.noWeaverSelection,
      [GlobalEnum.NoInspectorSelection]: settings.noInspectorSelection,
    }

    return await updateConfigValues(updateConfigs)
  }

  return {
    getAllSettings,
    saveAllSettings,
  }
}

// ========== 示例3: 电子秤设置专用 ==========

export function electronicScaleExample() {
  const { getElectronicScaleSettings, saveElectronicScaleSettings } = useElectronicScaleConfig()
  const settings = reactive({
    autoSave: false,
    autoSaveSeconds: 3,
    noWeaverSelection: false,
    noInspectorSelection: false,
    weightReflection: false,
    dataHead: '',
    dataEnd: '',
    scanCodeReading: false,
    stableValue: 0,
  })

  // 加载设置
  async function loadSettings() {
    const scaleSettings = await getElectronicScaleSettings()
    Object.assign(settings, scaleSettings)
  }

  // 保存设置
  async function saveSettings() {
    return await saveElectronicScaleSettings(settings)
  }

  return {
    settings,
    loadSettings,
    saveSettings,
  }
}

// ========== 示例4: 类型转换工具使用 ==========

export function typeConversionExample() {
  // 字符串转布尔值
  const boolValue = ConfigValueConverter.toBoolean('true') // true
  const boolValue2 = ConfigValueConverter.toBoolean('false') // false
  const boolValue3 = ConfigValueConverter.toBoolean('1') // true

  // 字符串转数字
  const numValue = ConfigValueConverter.toNumber('123') // 123
  const numValue2 = ConfigValueConverter.toNumber('abc') // 0

  // 字符串转数组
  const arrayValue = ConfigValueConverter.toArray('a,b,c') // ['a', 'b', 'c']
  const arrayValue2 = ConfigValueConverter.toArray('a|b|c', '|') // ['a', 'b', 'c']

  // 字符串转数字数组
  const numArrayValue = ConfigValueConverter.toNumberArray('1,2,3') // [1, 2, 3]

  return {
    boolValue,
    boolValue2,
    boolValue3,
    numValue,
    numValue2,
    arrayValue,
    arrayValue2,
    numArrayValue,
  }
}

// ========== 示例5: 重构前后对比 ==========

// 重构前的代码（复杂且重复）
export function beforeRefactoring() {
  const { fetchData: getGlobalConfig, data: globalConfigData, success: globalConfigSuccess, msg: globalConfigMsg } = GetGlobalConfigDropdownList()
  const { fetchData: saveConfig } = BatchUpdateGlobalConfigList()

  const configIdMap = {
    autoSaveSeconds: GlobalEnum.AutoSaveSeconds,
    noWeaverSelection: GlobalEnum.NoWeaverSelection,
    noInspectorSelection: GlobalEnum.NoInspectorSelection,
    weightReflection: GlobalEnum.WeightReflection,
    dataHead: GlobalEnum.DataHead,
    dataEnd: GlobalEnum.DataEnd,
    scanCodeReading: GlobalEnum.ScanCodeReading,
    stableValue: GlobalEnum.StableValue,
    autoSave: GlobalEnum.AutoSave,
  }

  const electronicScaleSettings = reactive({
    autoSave: false,
    autoSaveSeconds: 3,
    noWeaverSelection: false,
    noInspectorSelection: false,
    weightReflection: false,
    dataHead: '',
    dataEnd: '',
    scanCodeReading: false,
    stableValue: 0,
  })

  // 获取设置数据（重构前）
  async function getSettingsData() {
    const ids = Object.values(configIdMap).join(',')
    await getGlobalConfig({
      ids,
    })

    if (globalConfigSuccess.value)
      applyGlobalConfigToSettings()
    else
      ElMessage.error(globalConfigMsg.value)
  }

  // 应用配置到设置（重构前）
  function applyGlobalConfigToSettings() {
    if (!globalConfigData.value?.list)
      return

    globalConfigData.value.list.forEach((config) => {
      switch (config.id) {
        case configIdMap.autoSave:
          electronicScaleSettings.autoSave = config.options === 'true'
          break
        case configIdMap.autoSaveSeconds:
          electronicScaleSettings.autoSaveSeconds = Number(config.options) || 3
          break
        case configIdMap.noWeaverSelection:
          electronicScaleSettings.noWeaverSelection = config.options === 'true'
          break
        case configIdMap.noInspectorSelection:
          electronicScaleSettings.noInspectorSelection = config.options === 'true'
          break
        case configIdMap.weightReflection:
          electronicScaleSettings.weightReflection = config.options === 'true'
          break
        case configIdMap.dataHead:
          electronicScaleSettings.dataHead = config.options || ''
          break
        case configIdMap.dataEnd:
          electronicScaleSettings.dataEnd = config.options || ''
          break
        case configIdMap.scanCodeReading:
          electronicScaleSettings.scanCodeReading = config.options === 'true'
          break
        case configIdMap.stableValue:
          electronicScaleSettings.stableValue = Number(config.options) || 0
          break
      }
    })
  }

  return {
    electronicScaleSettings,
    getSettingsData,
  }
}

// 重构后的代码（简洁且易维护）
export function afterRefactoring() {
  const { getElectronicScaleSettings, saveElectronicScaleSettings } = useElectronicScaleConfig()

  const electronicScaleSettings = reactive({
    autoSave: false,
    autoSaveSeconds: 3,
    noWeaverSelection: false,
    noInspectorSelection: false,
    weightReflection: false,
    dataHead: '',
    dataEnd: '',
    scanCodeReading: false,
    stableValue: 0,
  })

  // 获取设置数据（重构后）
  async function getSettingsData() {
    const settings = await getElectronicScaleSettings()
    Object.assign(electronicScaleSettings, settings)
  }

  // 保存设置数据（重构后）
  async function saveSettingsData() {
    return await saveElectronicScaleSettings(electronicScaleSettings)
  }

  return {
    electronicScaleSettings,
    getSettingsData,
    saveSettingsData,
  }
}

// ========== 示例6: 在组件中的完整使用 ==========

export function componentUsageExample() {
  const { getConfigValue, updateConfigValue, loading } = useGlobalConfig()
  const isSyncToDefectData = ref(false)

  // 组件挂载时获取配置
  onMounted(async () => {
    // 一行代码获取配置，自动处理错误和类型转换
    isSyncToDefectData.value = await getConfigValue<boolean>(
      GlobalEnum.IsSyncToDefectData,
      false, // 默认值
    )
  })

  // 切换配置值
  async function toggleSyncToDefectData() {
    const newValue = !isSyncToDefectData.value
    const success = await updateConfigValue(GlobalEnum.IsSyncToDefectData, newValue)
    if (success)
      isSyncToDefectData.value = newValue
  }

  return {
    isSyncToDefectData,
    toggleSyncToDefectData,
    loading,
  }
}
