<script setup lang="ts">
import { ref } from 'vue'
import { useToggle } from '@vueuse/core'
import DefectEntryPanel from '@/components/DefectEntryPanel/index.vue'
import type { DefectFormData } from '@/components/DefectEntryPanel/types'
import { DefectEntryPresets } from '@/components/DefectEntryPanel/types'

const emits = defineEmits(['handleSure', 'onHide'])

// 使用通用疵点录入组件
const defectEntryRef = ref()
const [isOther, setIsOther] = useToggle(false)
const visible = ref(false)

// 兼容原有接口
const state = {
  get showModal() {
    return visible.value
  },
  set showModal(value: boolean) {
    visible.value = value
  },
  get form() {
    return defectEntryRef.value?.state?.form || {}
  },
}

function showAddDialog(row: any, isOtherType: boolean, isAdd: boolean) {
  setIsOther(isOtherType)
  defectEntryRef.value?.showDialog(row, isOtherType, isAdd)
}

// 设置疵点位置
function setDefectPosition(position: number) {
  defectEntryRef.value?.setDefectPosition(position)
}

function setData(formData: any) {
  defectEntryRef.value?.setFormData(formData)
  visible.value = true
}

// 处理提交
function handleSubmit(data: DefectFormData) {
  emits('handleSure', data)
}

// 处理隐藏
function handleHide() {
  emits('onHide')
}

defineExpose({
  showAddDialog,
  setData,
  setDefectPosition,
  state,
})
</script>

<template>
  <DefectEntryPanel
    ref="defectEntryRef"
    v-model:visible="visible"
    :modal-mode="true"
    modal-title="疵点信息"
    :is-other="isOther"
    :show-upload="true"
    :show-position="true"
    :show-category="false"
    :show-defect-code="false"
    @submit="handleSubmit"
    @hide="handleHide"
  />
</template>
