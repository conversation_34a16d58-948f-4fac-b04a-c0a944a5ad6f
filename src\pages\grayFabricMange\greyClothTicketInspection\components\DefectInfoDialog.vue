<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useToggle } from '@vueuse/core'
import UploadFile from '@/components/UploadFile/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'

const emits = defineEmits(['handleSure', 'onHide'])
const rules = reactive<any>({
  defect_count: [{ required: true, message: '请输入疵点个数', trigger: 'blur' }],
})

const state = reactive({
  showModal: false,
  modalName: '疵点信息',
  form: {
    name: '',
    defect_name: '',
    defect_id: 0, // 疵点ID
    defect_count: 1, // 疵点个数
    defect_position: 0, // 疵点位置 进位 10000
    measurement_unit_id: 0,
    measurement_unit_name: '',
    score: '1', // 分数
    voucher_files: [] as string[], // 上传凭证文件列表
  },
})

const [isOther, setIsOther] = useToggle(false)

function showAddDialog(row: any, isOther: boolean, isAdd: boolean) {
  state.showModal = true
  setIsOther(isOther)
  if (isAdd) {
    state.form = {
      ...row,
      id: void 0,
      defect_count: 1,
      defect_position: 0,
      score: '1',
      defect_id: row?.id || 0,
      defect_name: row?.name || '',
      voucher_files: [],
    }
    return
  }
  state.form = {
    ...row,
    voucher_files: row?.voucher_files || [],
  }
}

// 设置疵点位置
function setDefectPosition(position: number) {
  state.form.defect_position = position
}

const formRef = ref()
function handleSubmit() {
  formRef.value.validate((valid: boolean) => {
    if (!valid)
      return

    emits('handleSure', {
      ...state.form,
      defect_id: state.form.defect_id || 0,
      defect_count: Number(state.form.defect_count),
      defect_position: state.form.defect_position,
      score: Number(state.form.score),
      voucher_files: state.form.voucher_files,
    })
    state.showModal = false
  })
}

function setData(formData: any) {
  state.showModal = true
  state.form = {
    ...formData,
    voucher_files: formData?.voucher_files || [],
  }
}

function handleHide() {
  emits('onHide')
}

// 处理上传凭证文件变化
function handleVoucherFilesChange(fileList: string[]) {
  state.form.voucher_files = fileList
}

function unitSelectChange(val: any) {
  state.form.measurement_unit_name = val.name
}
defineExpose({
  showAddDialog,
  setData,
  setDefectPosition,
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="800" height="800" :mask="false" :lock-view="false" :esc-closable="true" resize @hide="handleHide">
    <div class="flex ">
      <el-form ref="formRef" size="large" :model="state.form" label-width="100px" label-position="right" :rules="rules">
        <el-form-item label="疵点名称:" prop="defect_name">
          <div v-if="isOther">
            <el-input v-model="state.form.defect_name" placeholder="请输入疵点名称" />
          </div>
          <span v-else>
            {{ state.form.name || state.form?.defect_name }}
          </span>
        </el-form-item>

        <el-form-item label="疵点位置:" prop="defect_position">
          <span>第</span>
          <el-input-number v-model="state.form.defect_position" style="width: 200px !important; margin: 0 10px;" />
          <span>米</span>
          <el-text type="info" size="small" style="margin-left: 10px;">
            (码表实时更新)
          </el-text>
        </el-form-item>
        <el-form-item label="单位名称:" prop="measurement_unit_id">
          <div v-if="isOther">
            <SelectComponents v-model="state.form.measurement_unit_id" style="width: 200px" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" clearable @change-value="unitSelectChange" />
          </div>
          <span v-else>
            {{ state.form.measurement_unit_name }}
          </span>
        </el-form-item>
        <el-form-item prop="defect_count" label="疵点数量:">
          <el-input-number v-model="state.form.defect_count" :min="0" :precision="0" placeholder="请输入疵点个数" style="width: 150px">
            <template #suffix>
              {{ state.form.measurement_unit_name }}
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="分数:" prop="score">
          <el-radio-group v-model="state.form.score">
            <el-radio-button label="1" value="1" />
            <el-radio-button label="2" value="2" />
            <el-radio-button label="3" value="3" />
            <el-radio-button label="4" value="4" />
          </el-radio-group>
        </el-form-item>

        <el-form-item label="上传凭证:">
          <UploadFile
            v-model:file-list="state.form.voucher_files"
            :multiple="true"
            :dragable="true"
            accept="image/*,.pdf,.doc,.docx"
            secene="defect_voucher"
            :auto-upload="true"
            :image-shown="true"
            additional-text="支持图片、PDF、Word文档"
            @on-upload-success="handleVoucherFilesChange"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.descriptions_row {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
