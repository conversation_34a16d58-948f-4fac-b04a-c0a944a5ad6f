# DefectEntryPanel 可复用疵点信息录入组件使用指南

## 概述

`DefectEntryPanel` 是一个可复用的疵点信息录入组件，用于替换成品质检和验布称重页面中重复的疵点录入UI和逻辑。该组件支持弹框模式和面板模式，提供灵活的配置选项。

## 组件特性

### ✨ **核心功能**
- 疵点类型选择（下拉框或选择器）
- 疵点数量输入
- 疵点位置输入
- 分数自动计算或手动输入
- 图片/凭证上传功能
- 表单验证逻辑
- 保存/确认/取消操作

### 🎛️ **灵活配置**
- **双模式支持**: 弹框模式 + 面板模式
- **可选字段**: 根据需求显示/隐藏特定字段
- **自定义验证**: 支持自定义表单验证规则
- **插槽扩展**: 提供插槽支持自定义内容
- **事件驱动**: 完整的事件系统

### 🔧 **技术特点**
- TypeScript 类型安全
- Vue 3 Composition API
- 响应式数据绑定
- 完整的 props 和 emits 类型定义

## 安装和导入

```typescript
import DefectEntryPanel from '@/components/DefectEntryPanel/index.vue'
import type { DefectFormData, DefectEntryConfig } from '@/components/DefectEntryPanel/types'
import { DefectEntryPresets } from '@/components/DefectEntryPanel/types'
```

## 基础用法

### 1. **弹框模式（验布称重页面）**

```vue
<template>
  <DefectEntryPanel
    ref="defectEntryRef"
    v-model:visible="showDialog"
    :modal-mode="true"
    modal-title="疵点信息"
    :is-other="isOther"
    :show-upload="true"
    :show-position="true"
    :show-category="false"
    :show-defect-code="false"
    @submit="handleDefectSubmit"
    @hide="handleDialogHide"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DefectEntryPanel from '@/components/DefectEntryPanel/index.vue'
import type { DefectFormData } from '@/components/DefectEntryPanel/types'

const defectEntryRef = ref()
const showDialog = ref(false)
const isOther = ref(false)

// 显示疵点录入弹框
function showDefectDialog(defectData: any, isOtherType = false) {
  isOther.value = isOtherType
  defectEntryRef.value?.showDialog(defectData, isOtherType, true)
}

// 处理疵点提交
function handleDefectSubmit(data: DefectFormData) {
  console.log('疵点数据:', data)
  // 处理提交逻辑
}

// 处理弹框隐藏
function handleDialogHide() {
  console.log('弹框已隐藏')
}
</script>
```

### 2. **面板模式（成品质检页面）**

```vue
<template>
  <div class="defect-panel-container">
    <DefectEntryPanel
      ref="defectEntryRef"
      :modal-mode="false"
      :is-other="isOther"
      :show-upload="true"
      :show-position="true"
      :show-category="true"
      :show-defect-code="true"
      @submit="handleDefectSubmit"
    >
      <!-- 自定义头部内容 -->
      <template #header>
        <div class="panel-header">
          <h3>疵点信息录入</h3>
        </div>
      </template>

      <!-- 自定义中间内容 -->
      <template #middle>
        <el-form-item label="缸号">
          {{ dyelotNumber }}
        </el-form-item>
        <el-form-item label="卷号">
          {{ volumeNumber }}
        </el-form-item>
      </template>

      <!-- 自定义底部按钮 -->
      <template #footer>
        <div class="custom-buttons">
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </DefectEntryPanel>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DefectEntryPanel from '@/components/DefectEntryPanel/index.vue'
import type { DefectFormData } from '@/components/DefectEntryPanel/types'

const defectEntryRef = ref()
const isOther = ref(false)
const dyelotNumber = ref('DY001')
const volumeNumber = ref('V001')

// 处理疵点提交
function handleDefectSubmit(data: DefectFormData) {
  console.log('疵点数据:', data)
  // 处理提交逻辑
}

// 自定义按钮处理
function handleReset() {
  defectEntryRef.value?.setFormData({
    defect_count: 1,
    defect_position: 0,
    score: 1,
  })
}

function handleCancel() {
  // 取消逻辑
}

function handleSave() {
  defectEntryRef.value?.handleSubmit()
}
</script>
```

## 配置选项

### Props 配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modalMode` | `boolean` | `false` | 是否显示为弹框模式 |
| `modalTitle` | `string` | `'疵点信息'` | 弹框标题 |
| `visible` | `boolean` | `false` | 是否显示弹框 |
| `modelValue` | `DefectFormData` | `{}` | 疵点数据 |
| `isOther` | `boolean` | `false` | 是否为"其他"疵点类型 |
| `isEdit` | `boolean` | `false` | 是否为编辑模式 |
| `showUpload` | `boolean` | `true` | 是否显示上传功能 |
| `showPosition` | `boolean` | `true` | 是否显示疵点位置 |
| `showCategory` | `boolean` | `false` | 是否显示疵点类别 |
| `showDefectCode` | `boolean` | `false` | 是否显示疵点编号 |
| `scoreOptions` | `number[]` | `[1,2,3,4]` | 分数选项 |
| `uploadAccept` | `string` | `'image/*,.pdf,.doc,.docx'` | 上传文件类型 |
| `uploadScene` | `string` | `'defect'` | 上传场景 |
| `customRules` | `object` | `{}` | 自定义验证规则 |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `submit` | `(data: DefectFormData)` | 提交疵点数据 |
| `cancel` | `()` | 取消操作 |
| `hide` | `()` | 隐藏弹框 |
| `update:visible` | `(value: boolean)` | 更新显示状态 |
| `update:modelValue` | `(value: DefectFormData)` | 更新数据 |

### 实例方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| `showDialog` | `(data?, isOther?, isAdd?)` | 显示弹框 |
| `setDefectPosition` | `(position: number)` | 设置疵点位置 |
| `setFormData` | `(data: DefectFormData)` | 设置表单数据 |
| `handleSubmit` | `()` | 提交表单 |

## 预设配置

组件提供了几种预设配置，可以快速应用到不同场景：

```typescript
import { DefectEntryPresets } from '@/components/DefectEntryPanel/types'

// 验布称重页面配置
const inspectionConfig = DefectEntryPresets.inspection

// 成品质检页面配置
const qualityCheckConfig = DefectEntryPresets.qualityCheck

// 简化配置
const simpleConfig = DefectEntryPresets.simple
```

### 预设配置详情

```typescript
// 验布称重页面配置
inspection: {
  modalMode: true,
  modalTitle: '疵点信息',
  showUpload: true,
  showPosition: true,
  showCategory: false,
  showDefectCode: false,
  scoreOptions: [1, 2, 3, 4],
  uploadAccept: 'image/*,.pdf,.doc,.docx',
  uploadScene: 'defect',
}

// 成品质检页面配置
qualityCheck: {
  modalMode: false,
  showUpload: true,
  showPosition: true,
  showCategory: true,
  showDefectCode: true,
  scoreOptions: [1, 2, 3, 4],
  uploadAccept: 'image/*,.pdf,.doc,.docx',
  uploadScene: 'defect',
}
```

## 数据类型

### DefectFormData 接口

```typescript
interface DefectFormData {
  id?: number                    // 记录ID
  name?: string                  // 疵点名称
  defect_name?: string          // 疵点名称（API字段）
  defect_code?: string          // 疵点编号
  defect_id?: number            // 疵点ID
  defect_count?: number | string // 疵点数量
  defect_position?: number | string // 疵点位置
  measurement_unit_id?: number   // 计量单位ID
  measurement_unit_name?: string // 计量单位名称
  score?: number | string       // 分数
  kind_id?: number              // 疵点类别ID
  voucher_files?: string[]      // 上传凭证文件列表
  uploadFiles?: string[]        // 上传文件列表
  [key: string]: any           // 其他扩展字段
}
```

## 高级用法

### 1. **自定义验证规则**

```vue
<template>
  <DefectEntryPanel
    :custom-rules="customRules"
    @submit="handleSubmit"
  />
</template>

<script setup lang="ts">
const customRules = {
  defect_count: [
    { required: true, message: '请输入疵点个数', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '疵点个数必须在1-100之间', trigger: 'blur' }
  ],
  defect_position: [
    { required: true, message: '请输入疵点位置', trigger: 'blur' },
    { type: 'number', min: 0, message: '疵点位置不能为负数', trigger: 'blur' }
  ]
}
</script>
```

### 2. **动态配置**

```vue
<template>
  <DefectEntryPanel
    :show-upload="showUploadFeature"
    :show-position="showPositionFeature"
    :score-options="dynamicScoreOptions"
    @submit="handleSubmit"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'

const userRole = ref('admin')

const showUploadFeature = computed(() => userRole.value === 'admin')
const showPositionFeature = computed(() => userRole.value !== 'viewer')
const dynamicScoreOptions = computed(() => 
  userRole.value === 'admin' ? [1, 2, 3, 4, 5] : [1, 2, 3]
)
</script>
```

### 3. **与外部数据联动**

```vue
<template>
  <DefectEntryPanel
    ref="defectEntryRef"
    v-model="defectData"
    @submit="handleSubmit"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const defectData = ref<DefectFormData>({})
const meterPosition = ref(0)

// 监听码表位置变化，自动更新疵点位置
watch(meterPosition, (newPosition) => {
  defectEntryRef.value?.setDefectPosition(newPosition)
})

// 监听疵点数据变化
watch(defectData, (newData) => {
  console.log('疵点数据变化:', newData)
}, { deep: true })
</script>
```

## 迁移指南

### 从 DefectInfoDialog 迁移

**原有代码：**
```vue
<DefectInfoDialog
  ref="defectInfoDialogRef"
  @handleSure="handleDefectSure"
  @onHide="handleDefectDialogClose"
/>
```

**迁移后：**
```vue
<DefectEntryPanel
  ref="defectEntryRef"
  v-model:visible="showDialog"
  :modal-mode="true"
  modal-title="疵点信息"
  :is-other="isOther"
  @submit="handleDefectSure"
  @hide="handleDefectDialogClose"
/>
```

### 从成品质检页面迁移

**原有代码：**
```vue
<!-- 复杂的表单结构 -->
<el-form ref="defectFormRef" :model="defectForm" :rules="defectFormRules">
  <!-- 大量重复的表单项 -->
</el-form>
```

**迁移后：**
```vue
<DefectEntryPanel
  :modal-mode="false"
  :is-other="isOther"
  :show-category="true"
  :show-defect-code="true"
  @submit="handleDefectSubmit"
>
  <template #header>
    <!-- 自定义头部内容 -->
  </template>
</DefectEntryPanel>
```

## 最佳实践

### 1. **组件复用**
- 在多个页面中使用相同的配置时，创建预设配置
- 使用 TypeScript 确保类型安全
- 合理使用插槽扩展功能

### 2. **性能优化**
- 使用 `v-model` 进行双向数据绑定
- 避免在模板中使用复杂的计算逻辑
- 合理使用 `ref` 和 `reactive`

### 3. **错误处理**
- 提供完整的表单验证
- 处理异步操作的错误情况
- 给用户友好的错误提示

## 总结

`DefectEntryPanel` 组件成功实现了疵点录入功能的统一和复用，具有以下优势：

- **✅ 代码复用** - 减少重复代码，提高开发效率
- **✅ 统一体验** - 保证不同页面的一致性
- **✅ 灵活配置** - 支持多种使用场景
- **✅ 类型安全** - 完整的 TypeScript 支持
- **✅ 易于维护** - 集中管理疵点录入逻辑

该组件为项目中的疵点管理功能提供了标准化的解决方案，为未来的功能扩展和维护奠定了良好的基础。
