// 疵点表单数据类型
export interface DefectFormData {
  /** 记录ID */
  id?: number
  /** 疵点名称 */
  name?: string
  /** 疵点名称（API字段） */
  defect_name?: string
  /** 疵点编号 */
  defect_code?: string
  /** 疵点ID */
  defect_id?: number
  /** 疵点数量 */
  defect_count?: number | string
  /** 疵点位置 */
  defect_position?: number | string
  /** 计量单位ID */
  measurement_unit_id?: number
  /** 计量单位名称 */
  measurement_unit_name?: string
  /** 分数 */
  score?: number | string
  /** 疵点类别ID */
  kind_id?: number
  /** 上传凭证文件列表 */
  voucher_files?: string[]
  /** 上传文件列表 */
  uploadFiles?: string[]
  /** 其他扩展字段 */
  [key: string]: any
}

// 组件配置选项
export interface DefectEntryConfig {
  /** 是否显示为弹框模式 */
  modalMode?: boolean
  /** 弹框标题 */
  modalTitle?: string
  /** 是否为"其他"疵点类型 */
  isOther?: boolean
  /** 是否为编辑模式 */
  isEdit?: boolean
  /** 是否显示上传功能 */
  showUpload?: boolean
  /** 是否显示疵点位置 */
  showPosition?: boolean
  /** 是否显示疵点类别 */
  showCategory?: boolean
  /** 是否显示疵点编号 */
  showDefectCode?: boolean
  /** 是否显示数字键盘 */
  showDigitalKeyboard?: boolean
  /** 是否显示分割器 */
  showSplitter?: boolean
  /** 初始分割比例 */
  initialSplit?: number
  /** 额外的表单字段显示配置 */
  extraFields?: string[]
  /** 分数选项 */
  scoreOptions?: number[]
  /** 上传文件类型 */
  uploadAccept?: string
  /** 上传场景 */
  uploadScene?: string
}

// 表单验证规则类型
export interface DefectFormRules {
  defect_count?: any[]
  defect_name?: any[]
  defect_position?: any[]
  defect_code?: any[]
  measurement_unit_id?: any[]
  score?: any[]
  kind_id?: any[]
  [key: string]: any[]
}

// 组件事件类型
export interface DefectEntryEvents {
  /** 提交事件 */
  submit: (data: DefectFormData) => void
  /** 取消事件 */
  cancel: () => void
  /** 隐藏事件 */
  hide: () => void
  /** 数据更新事件 */
  'update:modelValue': (value: DefectFormData) => void
  /** 显示状态更新事件 */
  'update:visible': (value: boolean) => void
}

// 组件实例方法类型
export interface DefectEntryInstance {
  /** 显示弹框 */
  showDialog: (data?: DefectFormData, isOther?: boolean, isAdd?: boolean) => void
  /** 设置疵点位置 */
  setDefectPosition: (position: number) => void
  /** 设置表单数据 */
  setFormData: (data: DefectFormData) => void
  /** 提交表单 */
  handleSubmit: () => void
  /** 内部状态 */
  state: {
    showModal: boolean
    form: DefectFormData
  }
  /** 表单引用 */
  formRef: any
}

// 预设配置
export const DefectEntryPresets = {
  // 验布称重页面配置
  inspection: {
    modalMode: true,
    modalTitle: '疵点信息',
    showUpload: true,
    showPosition: true,
    showCategory: false,
    showDefectCode: false,
    showDigitalKeyboard: true,
    showSplitter: true,
    initialSplit: 65,
    scoreOptions: [1, 2, 3, 4],
    uploadAccept: 'image/*,.pdf,.doc,.docx',
    uploadScene: 'defect',
  } as DefectEntryConfig,

  // 成品质检页面配置
  qualityCheck: {
    modalMode: false,
    showUpload: true,
    showPosition: true,
    showCategory: true,
    showDefectCode: true,
    showDigitalKeyboard: true,
    showSplitter: true,
    initialSplit: 65,
    scoreOptions: [1, 2, 3, 4],
    uploadAccept: 'image/*,.pdf,.doc,.docx',
    uploadScene: 'defect',
  } as DefectEntryConfig,

  // 简化配置（仅基础字段）
  simple: {
    modalMode: true,
    modalTitle: '疵点信息',
    showUpload: false,
    showPosition: false,
    showCategory: false,
    showDefectCode: false,
    showDigitalKeyboard: false,
    showSplitter: false,
    scoreOptions: [1, 2, 3, 4],
  } as DefectEntryConfig,
}

// 默认表单数据
export const defaultDefectFormData: DefectFormData = {
  name: '',
  defect_name: '',
  defect_code: '',
  defect_id: 0,
  defect_count: 1,
  defect_position: 0,
  measurement_unit_id: 0,
  measurement_unit_name: '',
  score: 1,
  kind_id: 0,
  voucher_files: [],
  uploadFiles: [],
}

// 默认验证规则
export const defaultDefectFormRules: DefectFormRules = {
  defect_count: [{ required: true, message: '请输入疵点个数', trigger: 'blur' }],
  defect_name: [{ required: true, message: '请输入疵点名称', trigger: 'blur' }],
  defect_position: [{ required: true, message: '请输入疵点位置', trigger: 'blur' }],
}
